export const inspectionData = [
  {
    id: 1,
    inspectionsList: [
      {
        id: 1,
        propertyName: "Sunset Apartments",
        propertyAddress: "Unit 101, Sunset Apartments, Dubai Marina",
        tenantName: "<PERSON>",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-123-4567",
        inspectorName: "<PERSON>",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-987-6543",
        inspectionType: "Routine",
        requestedDate: "2024-06-15",
        scheduledDate: "2024-06-20",
        completedDate: null,
        status: "Pending",
        priority: "Medium",
        notes: "Regular quarterly inspection",
        findings: [],
        overallCondition: null,
        recommendations: [],
        nextInspectionDate: null,
        photos: [],
        createdBy: "Property Manager",
        createdDate: "2024-06-10",
        lastUpdated: "2024-06-10",
        areasInspected: [],
        inspectionDuration: null,
        reportGenerated: false,
        reportUrl: null,
        tags: ["routine", "quarterly"],
      },
      {
        id: 2,
        propertyName: "Oak Grove Condos",
        propertyAddress: "Unit 205, Oak Grove Condos, Business Bay",
        tenantName: "<PERSON>",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-234-5678",
        inspectorName: "<PERSON>",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-876-5432",
        inspectionType: "Maintenance",
        requestedDate: "2024-06-20",
        scheduledDate: "2024-06-25",
        completedDate: null,
        status: "Scheduled",
        priority: "High",
        notes: "Tenant reported water leak in kitchen",
        findings: [],
        overallCondition: null,
        recommendations: [],
        nextInspectionDate: null,
        photos: [],
        createdBy: "Tenant Request",
        createdDate: "2024-06-18",
        lastUpdated: "2024-06-19",
        areasInspected: [],
        inspectionDuration: null,
        reportGenerated: false,
        reportUrl: null,
        tags: ["maintenance", "urgent", "water-leak"],
      },
      {
        id: 3,
        propertyName: "Marina View Apartments",
        propertyAddress: "Unit 1502, Marina View Apartments, Dubai Marina",
        tenantName: "Robert Johnson",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-345-6789",
        inspectorName: "Mike Wilson",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-987-6543",
        inspectionType: "Move-out",
        requestedDate: "2024-05-10",
        scheduledDate: "2024-05-15",
        completedDate: "2024-05-15",
        status: "Completed",
        priority: "Medium",
        notes: "Final inspection before tenant move-out",
        findings: [
          {
            area: "Living Room",
            condition: "Good",
            issues: [],
            notes: "Clean and well maintained",
          },
          {
            area: "Kitchen",
            condition: "Fair",
            issues: ["Cabinet door scratches", "Minor wear on cabinet doors"],
            notes: "Minor wear on cabinet doors",
          },
          {
            area: "Bedroom",
            condition: "Good",
            issues: [],
            notes: "Excellent condition",
          },
          {
            area: "Bathroom",
            condition: "Good",
            issues: [],
            notes: "Clean and functional",
          },
        ],
        overallCondition: "Good",
        recommendations: [
          "Replace cabinet hardware",
          "Touch up paint in kitchen",
        ],
        nextInspectionDate: "2024-08-15",
        photos: [
          {
            id: 1,
            area: "Kitchen",
            url: "/images/kitchen-inspection.jpg",
            description: "Cabinet door condition",
          },
          {
            id: 2,
            area: "Living Room",
            url: "/images/living-room.jpg",
            description: "Overall condition",
          },
        ],
        createdBy: "Property Manager",
        createdDate: "2024-05-08",
        lastUpdated: "2024-05-15",
        areasInspected: [
          "Living Room",
          "Kitchen",
          "Bedroom",
          "Bathroom",
          "Balcony",
        ],
        inspectionDuration: "2 hours",
        reportGenerated: true,
        reportUrl: "/reports/inspection-3-report.pdf",
        tags: ["move-out", "completed", "deposit-assessment"],
      },
    ],
  },
];

export const inspectionTypes = [
  "Routine",
  "Maintenance",
  "Move-in",
  "Move-out",
  "Emergency",
  "Annual",
  "Quarterly",
  "Pre-lease",
  "Damage Assessment",
  "Safety Inspection",
  "Compliance Check",
];

export const inspectionStatuses = [
  {
    value: "Pending",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "Scheduled",
    color: "blue",
    bgColor: "bg-blue-100",
    textColor: "text-blue-700",
  },
  {
    value: "In Progress",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Completed",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "Cancelled",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
  {
    value: "Rescheduled",
    color: "purple",
    bgColor: "bg-purple-100",
    textColor: "text-purple-700",
  },
];

export const priorityLevels = [
  {
    value: "Low",
    color: "gray",
    bgColor: "bg-gray-100",
    textColor: "text-gray-700",
  },
  {
    value: "Medium",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "High",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Critical",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const conditionLevels = [
  {
    value: "Excellent",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "Good",
    color: "blue",
    bgColor: "bg-blue-100",
    textColor: "text-blue-700",
  },
  {
    value: "Fair",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "Poor",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Critical",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const inspectionAreas = [
  "Living Room",
  "Kitchen",
  "Bedroom",
  "Bathroom",
  "Balcony",
  "Laundry Room",
  "Storage",
  "Parking",
  "Common Areas",
  "HVAC System",
  "Plumbing",
  "Electrical",
  "Windows",
  "Doors",
  "Flooring",
  "Walls",
  "Ceiling",
];

export const inspectorsList = [
  {
    id: 1,
    name: "Mike Wilson",
    email: "<EMAIL>",
    phone: "+971-55-987-6543",
    specialization: ["Routine", "Move-out", "Maintenance"],
    rating: 4.8,
    completedInspections: 156,
    availability: "Available",
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+971-55-876-5432",
    specialization: ["Emergency", "Safety Inspection", "Compliance Check"],
    rating: 4.9,
    completedInspections: 203,
    availability: "Available",
  },
  {
    id: 3,
    name: "David Chen",
    email: "<EMAIL>",
    phone: "+971-55-765-4321",
    specialization: ["Annual", "Pre-lease", "Damage Assessment"],
    rating: 4.7,
    completedInspections: 89,
    availability: "Busy",
  },
];
