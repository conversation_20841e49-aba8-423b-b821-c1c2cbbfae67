export const inspectionData = [
  {
    id: 1,
    inspectionsList: [
      {
        id: 1,
        propertyName: "Sunset Apartments",
        propertyAddress: "Unit 101, Sunset Apartments, Dubai Marina",
        tenantName: "<PERSON>",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-123-4567",
        inspectorName: "<PERSON>",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-987-6543",
        inspectionType: "Routine",
        requestedDate: "2024-06-15",
        scheduledDate: "2024-06-20",
        completedDate: null,
        status: "Pending Tenant Response",
        priority: "Medium",
        notes: "Regular quarterly inspection",
        findings: [],
        overallCondition: null,
        recommendations: [],
        nextInspectionDate: null,
        photos: [],
        createdBy: "Property Manager",
        createdDate: "2024-06-10",
        lastUpdated: "2024-06-10",
        areasInspected: [],
        inspectionDuration: null,
        reportGenerated: false,
        reportUrl: null,
        tags: ["routine", "quarterly"],
        // Enhanced workflow fields
        tenantResponse: null, // "accepted", "declined", "reschedule_requested"
        tenantResponseDate: null,
        tenantDeclineReason: null,
        tenantSuggestedTimes: [],
        landlordNotes: "Regular quarterly inspection",
        inspectionReport: null,
        reportSharedWithTenant: false,
        tenantComments: [],
      },
      {
        id: 2,
        propertyName: "Oak Grove Condos",
        propertyAddress: "Unit 205, Oak Grove Condos, Business Bay",
        tenantName: "Jane Smith",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-234-5678",
        inspectorName: "Sarah Johnson",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-876-5432",
        inspectionType: "Maintenance",
        requestedDate: "2024-06-20",
        scheduledDate: "2024-06-25",
        completedDate: null,
        status: "Confirmed",
        priority: "High",
        notes: "Tenant reported water leak in kitchen",
        findings: [],
        overallCondition: null,
        recommendations: [],
        nextInspectionDate: null,
        photos: [],
        createdBy: "Tenant Request",
        createdDate: "2024-06-18",
        lastUpdated: "2024-06-19",
        areasInspected: [],
        inspectionDuration: null,
        reportGenerated: false,
        reportUrl: null,
        tags: ["maintenance", "urgent", "water-leak"],
        // Enhanced workflow fields
        tenantResponse: "accepted",
        tenantResponseDate: "2024-06-19",
        tenantDeclineReason: null,
        tenantSuggestedTimes: [],
        landlordNotes:
          "Tenant reported water leak in kitchen - urgent inspection required",
        inspectionReport: null,
        reportSharedWithTenant: false,
        tenantComments: [],
      },
      {
        id: 3,
        propertyName: "Marina View Apartments",
        propertyAddress: "Unit 1502, Marina View Apartments, Dubai Marina",
        tenantName: "Robert Johnson",
        tenantEmail: "<EMAIL>",
        tenantPhone: "+971-50-345-6789",
        inspectorName: "Mike Wilson",
        inspectorEmail: "<EMAIL>",
        inspectorPhone: "+971-55-987-6543",
        inspectionType: "Move-out",
        requestedDate: "2024-05-10",
        scheduledDate: "2024-05-15",
        completedDate: "2024-05-15",
        status: "Report Generated",
        priority: "Medium",
        notes: "Final inspection before tenant move-out",
        findings: [
          {
            area: "Living Room",
            condition: "Good",
            issues: [],
            notes: "Clean and well maintained",
          },
          {
            area: "Kitchen",
            condition: "Fair",
            issues: ["Cabinet door scratches", "Minor wear on cabinet doors"],
            notes: "Minor wear on cabinet doors",
          },
          {
            area: "Bedroom",
            condition: "Good",
            issues: [],
            notes: "Excellent condition",
          },
          {
            area: "Bathroom",
            condition: "Good",
            issues: [],
            notes: "Clean and functional",
          },
        ],
        overallCondition: "Good",
        recommendations: [
          "Replace cabinet hardware",
          "Touch up paint in kitchen",
        ],
        nextInspectionDate: "2024-08-15",
        photos: [
          {
            id: 1,
            area: "Kitchen",
            url: "/images/kitchen-inspection.jpg",
            description: "Cabinet door condition",
          },
          {
            id: 2,
            area: "Living Room",
            url: "/images/living-room.jpg",
            description: "Overall condition",
          },
        ],
        createdBy: "Property Manager",
        createdDate: "2024-05-08",
        lastUpdated: "2024-05-15",
        areasInspected: [
          "Living Room",
          "Kitchen",
          "Bedroom",
          "Bathroom",
          "Balcony",
        ],
        inspectionDuration: "2 hours",
        reportGenerated: true,
        reportUrl: "/reports/inspection-3-report.pdf",
        tags: ["move-out", "completed", "deposit-assessment"],
        // Enhanced workflow fields
        tenantResponse: "accepted",
        tenantResponseDate: "2024-05-12",
        tenantDeclineReason: null,
        tenantSuggestedTimes: [],
        landlordNotes:
          "Final inspection before tenant move-out for deposit assessment",
        inspectionReport: {
          id: 1,
          generatedDate: "2024-05-15",
          generatedBy: "Property Manager",
          overallScore: 85,
          roomAssessments: [
            {
              room: "Living Room",
              condition: "Good",
              score: 90,
              issues: [],
              photos: [
                "/images/living-room-1.jpg",
                "/images/living-room-2.jpg",
              ],
              notes: "Clean and well maintained",
            },
            {
              room: "Kitchen",
              condition: "Fair",
              score: 75,
              issues: [
                {
                  severity: "Minor",
                  description: "Cabinet door scratches",
                  cost: 150,
                },
                {
                  severity: "Minor",
                  description: "Minor wear on cabinet doors",
                  cost: 100,
                },
              ],
              photos: ["/images/kitchen-1.jpg", "/images/kitchen-2.jpg"],
              notes: "Minor wear on cabinet doors, needs touch-up",
            },
            {
              room: "Bedroom",
              condition: "Good",
              score: 95,
              issues: [],
              photos: ["/images/bedroom-1.jpg"],
              notes: "Excellent condition",
            },
            {
              room: "Bathroom",
              condition: "Good",
              score: 88,
              issues: [],
              photos: ["/images/bathroom-1.jpg"],
              notes: "Clean and functional",
            },
          ],
          maintenanceRecommendations: [
            {
              priority: "Medium",
              description: "Replace cabinet hardware",
              estimatedCost: 200,
            },
            {
              priority: "Low",
              description: "Touch up paint in kitchen",
              estimatedCost: 150,
            },
          ],
          landlordObservations:
            "Property is in good overall condition. Minor cosmetic issues in kitchen.",
          nextInspectionRecommended: "2024-11-15",
        },
        reportSharedWithTenant: true,
        tenantComments: [
          {
            id: 1,
            comment:
              "I agree with the assessment. The kitchen issues were pre-existing.",
            date: "2024-05-16",
            type: "agreement",
          },
        ],
      },
    ],
  },
];

export const inspectionTypes = [
  "Routine",
  "Maintenance",
  "Move-in",
  "Move-out",
  "Emergency",
  "Annual",
  "Quarterly",
  "Pre-lease",
  "Damage Assessment",
  "Safety Inspection",
  "Compliance Check",
];

export const inspectionStatuses = [
  {
    value: "Pending Tenant Response",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "Tenant Accepted",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "Tenant Declined",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
  {
    value: "Reschedule Requested",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Confirmed",
    color: "blue",
    bgColor: "bg-blue-100",
    textColor: "text-blue-700",
  },
  {
    value: "In Progress",
    color: "purple",
    bgColor: "bg-purple-100",
    textColor: "text-purple-700",
  },
  {
    value: "Completed",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "Report Generated",
    color: "blue",
    bgColor: "bg-blue-100",
    textColor: "text-blue-700",
  },
  {
    value: "Cancelled",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const priorityLevels = [
  {
    value: "Low",
    color: "gray",
    bgColor: "bg-gray-100",
    textColor: "text-gray-700",
  },
  {
    value: "Medium",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "High",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Critical",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const conditionLevels = [
  {
    value: "Excellent",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "Good",
    color: "blue",
    bgColor: "bg-blue-100",
    textColor: "text-blue-700",
  },
  {
    value: "Fair",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "Poor",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Critical",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const inspectionAreas = [
  "Living Room",
  "Kitchen",
  "Bedroom",
  "Bathroom",
  "Balcony",
  "Laundry Room",
  "Storage",
  "Parking",
  "Common Areas",
  "HVAC System",
  "Plumbing",
  "Electrical",
  "Windows",
  "Doors",
  "Flooring",
  "Walls",
  "Ceiling",
];

export const issueSeverityLevels = [
  {
    value: "Minor",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-700",
  },
  {
    value: "Major",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
  {
    value: "Critical",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
];

export const tenantResponseTypes = [
  {
    value: "accepted",
    label: "Accepted",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-700",
  },
  {
    value: "declined",
    label: "Declined",
    color: "red",
    bgColor: "bg-red-100",
    textColor: "text-red-700",
  },
  {
    value: "reschedule_requested",
    label: "Reschedule Requested",
    color: "orange",
    bgColor: "bg-orange-100",
    textColor: "text-orange-700",
  },
];

export const inspectorsList = [
  {
    id: 1,
    name: "Mike Wilson",
    email: "<EMAIL>",
    phone: "+971-55-987-6543",
    specialization: ["Routine", "Move-out", "Maintenance"],
    rating: 4.8,
    completedInspections: 156,
    availability: "Available",
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+971-55-876-5432",
    specialization: ["Emergency", "Safety Inspection", "Compliance Check"],
    rating: 4.9,
    completedInspections: 203,
    availability: "Available",
  },
  {
    id: 3,
    name: "David Chen",
    email: "<EMAIL>",
    phone: "+971-55-765-4321",
    specialization: ["Annual", "Pre-lease", "Damage Assessment"],
    rating: 4.7,
    completedInspections: 89,
    availability: "Busy",
  },
];
