import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import ApplicationModals from "./ApplicationModals";
import {
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  MessageSquare,
  ArrowUpDown,
  Calendar,
  MapPin,
  User,
  Phone,
  Mail,
  Send,
  History,
  AlertCircle,
  TrendingUp,
  Eye,
  Edit,
  Save,
  X,
  Home,
  Star,
  FileText,
} from "lucide-react";

const ApplicationsManagement = () => {
  // State for applications and negotiations
  const [applications, setApplications] = useState([
    {
      id: 1,
      propertyId: "prop_001",
      propertyTitle: "Modern 2BR Apartment in Downtown",
      propertyAddress: "123 Main Street, Downtown, City",
      propertyImage: "/api/placeholder/400/300",
      tenantName: "<PERSON>",
      tenantEmail: "<EMAIL>",
      tenantPhone: "+****************",
      tenantRating: 4.8,
      originalPrice: 2500,
      currentOffer: 2350,
      tenantLastOffer: 2350,
      myLastOffer: 2400,
      status: "under_negotiation", // pending, accepted, declined, under_negotiation, agreed
      submittedDate: "2024-12-15",
      lastActivity: "2024-12-16",
      negotiationHistory: [
        {
          id: 1,
          amount: 2300,
          by: "tenant",
          date: "2024-12-15T10:00:00Z",
          message: "Initial offer",
        },
        {
          id: 2,
          amount: 2400,
          by: "landlord",
          date: "2024-12-15T14:30:00Z",
          message: "Counter offer",
        },
        {
          id: 3,
          amount: 2350,
          by: "tenant",
          date: "2024-12-16T09:15:00Z",
          message: "Counter offer",
        },
      ],
      canMessage: false,
      tenantInfo: {
        creditScore: 750,
        monthlyIncome: 8500,
        employmentStatus: "Full-time",
        previousRentals: 3,
        references: 2,
      },
    },
    {
      id: 2,
      propertyId: "prop_002",
      propertyTitle: "Cozy 1BR Studio Near University",
      propertyAddress: "456 College Ave, University District",
      propertyImage: "/api/placeholder/400/300",
      tenantName: "Michael Chen",
      tenantEmail: "<EMAIL>",
      tenantPhone: "+****************",
      tenantRating: 4.5,
      originalPrice: 1800,
      currentOffer: 1750,
      tenantLastOffer: 1750,
      myLastOffer: null,
      status: "pending",
      submittedDate: "2024-12-14",
      lastActivity: "2024-12-14",
      negotiationHistory: [
        {
          id: 1,
          amount: 1750,
          by: "tenant",
          date: "2024-12-14T16:20:00Z",
          message: "Initial offer",
        },
      ],
      canMessage: false,
      tenantInfo: {
        creditScore: 720,
        monthlyIncome: 6200,
        employmentStatus: "Full-time",
        previousRentals: 2,
        references: 3,
      },
    },
    {
      id: 3,
      propertyId: "prop_003",
      propertyTitle: "Luxury 3BR Penthouse with City View",
      propertyAddress: "789 Skyline Blvd, Uptown",
      propertyImage: "/api/placeholder/400/300",
      tenantName: "Emily Rodriguez",
      tenantEmail: "<EMAIL>",
      tenantPhone: "+****************",
      tenantRating: 5.0,
      originalPrice: 4500,
      currentOffer: 4500,
      tenantLastOffer: 4500,
      myLastOffer: 4500,
      status: "agreed",
      submittedDate: "2024-12-10",
      lastActivity: "2024-12-12",
      negotiationHistory: [
        {
          id: 1,
          amount: 4200,
          by: "tenant",
          date: "2024-12-10T11:00:00Z",
          message: "Initial offer",
        },
        {
          id: 2,
          amount: 4400,
          by: "landlord",
          date: "2024-12-11T09:30:00Z",
          message: "Counter offer",
        },
        {
          id: 3,
          amount: 4500,
          by: "tenant",
          date: "2024-12-12T14:15:00Z",
          message: "Final offer - agreed!",
        },
      ],
      canMessage: true,
      tenantInfo: {
        creditScore: 800,
        monthlyIncome: 12000,
        employmentStatus: "Full-time",
        previousRentals: 5,
        references: 4,
      },
    },
  ]);

  // Modal states
  const [negotiateModal, setNegotiateModal] = useState({
    open: false,
    applicationId: null,
  });
  const [historyModal, setHistoryModal] = useState({
    open: false,
    applicationId: null,
  });
  const [messageModal, setMessageModal] = useState({
    open: false,
    applicationId: null,
  });
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    applicationId: null,
    action: null,
  });
  const [tenantInfoModal, setTenantInfoModal] = useState({
    open: false,
    applicationId: null,
  });

  // Form states
  const [negotiateAmount, setNegotiateAmount] = useState("");
  const [negotiateMessage, setNegotiateMessage] = useState("");
  const [chatMessage, setChatMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Get status styling
  const getStatusStyle = (status) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "accepted":
        return "bg-green-100 text-green-800 border-green-200";
      case "declined":
        return "bg-red-100 text-red-800 border-red-200";
      case "under_negotiation":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "agreed":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "accepted":
        return <CheckCircle className="w-4 h-4" />;
      case "declined":
        return <XCircle className="w-4 h-4" />;
      case "under_negotiation":
        return <ArrowUpDown className="w-4 h-4" />;
      case "agreed":
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  // Format status text
  const getStatusText = (status) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "accepted":
        return "Accepted";
      case "declined":
        return "Declined";
      case "under_negotiation":
        return "Under Negotiation";
      case "agreed":
        return "Terms Agreed";
      default:
        return "Unknown";
    }
  };

  // Handle application actions
  const handleAcceptApplication = (applicationId) => {
    setConfirmModal({ open: true, applicationId, action: "accept" });
  };

  const handleDeclineApplication = (applicationId) => {
    setConfirmModal({ open: true, applicationId, action: "decline" });
  };

  const handleNegotiate = (applicationId) => {
    const application = applications.find((a) => a.id === applicationId);
    setNegotiateAmount(
      application.tenantLastOffer?.toString() ||
        application.originalPrice.toString()
    );
    setNegotiateMessage("");
    setNegotiateModal({ open: true, applicationId });
  };

  const submitNegotiation = () => {
    if (!negotiateAmount || parseFloat(negotiateAmount) <= 0) {
      alert("Please enter a valid amount");
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setApplications((prev) =>
        prev.map((application) => {
          if (application.id === negotiateModal.applicationId) {
            const newNegotiation = {
              id: application.negotiationHistory.length + 1,
              amount: parseFloat(negotiateAmount),
              by: "landlord",
              date: new Date().toISOString(),
              message: negotiateMessage || "Counter offer",
            };

            return {
              ...application,
              myLastOffer: parseFloat(negotiateAmount),
              currentOffer: parseFloat(negotiateAmount),
              status: "under_negotiation",
              lastActivity: new Date().toISOString().split("T")[0],
              negotiationHistory: [
                ...application.negotiationHistory,
                newNegotiation,
              ],
            };
          }
          return application;
        })
      );

      setIsLoading(false);
      setNegotiateModal({ open: false, applicationId: null });
      setNegotiateAmount("");
      setNegotiateMessage("");

      // Show success notification
      const notification = document.createElement("div");
      notification.className =
        "fixed z-50 px-6 py-3 text-white bg-green-500 rounded-lg shadow-lg top-4 right-4";
      notification.textContent = "Counter offer submitted successfully!";
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 4000);
    }, 1500);
  };

  const confirmAction = () => {
    const { applicationId, action } = confirmModal;
    setIsLoading(true);

    setTimeout(() => {
      setApplications((prev) =>
        prev.map((application) => {
          if (application.id === applicationId) {
            return {
              ...application,
              status: action === "accept" ? "accepted" : "declined",
              lastActivity: new Date().toISOString().split("T")[0],
            };
          }
          return application;
        })
      );

      setIsLoading(false);
      setConfirmModal({ open: false, applicationId: null, action: null });

      const notification = document.createElement("div");
      notification.className =
        "fixed z-50 px-6 py-3 text-white bg-green-500 rounded-lg shadow-lg top-4 right-4";
      notification.textContent = `Application ${action}ed successfully!`;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 4000);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Rental Applications
              </h1>
              <p className="text-gray-600">
                Manage incoming rental applications and negotiations
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="px-4 py-2 bg-white rounded-lg border border-gray-200 shadow-sm">
              <span className="text-sm font-medium text-gray-600">
                Total Applications:{" "}
              </span>
              <span className="text-sm font-bold text-blue-600">
                {applications.length}
              </span>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {applications.filter((a) => a.status === "pending").length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <ArrowUpDown className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Negotiating</p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    applications.filter((a) => a.status === "under_negotiation")
                      .length
                  }
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Agreed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {applications.filter((a) => a.status === "agreed").length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-red-100 rounded-lg">
                <XCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Declined</p>
                <p className="text-2xl font-bold text-gray-900">
                  {applications.filter((a) => a.status === "declined").length}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Applications List */}
        <div className="space-y-6">
          {applications.length === 0 ? (
            <Card className="p-12 text-center border-0 shadow-sm">
              <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Applications Yet
              </h3>
              <p className="text-gray-600 mb-4">
                You haven't received any rental applications yet.
              </p>
            </Card>
          ) : (
            applications.map((application) => (
              <Card
                key={application.id}
                className="p-6 border-0 shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Property & Tenant Info */}
                  <div className="lg:col-span-2">
                    <div className="flex gap-4">
                      <img
                        src={application.propertyImage}
                        alt={application.propertyTitle}
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {application.propertyTitle}
                          </h3>
                          <div
                            className={`px-3 py-1 rounded-full text-xs font-medium border flex items-center gap-1 ${getStatusStyle(
                              application.status
                            )}`}
                          >
                            {getStatusIcon(application.status)}
                            {getStatusText(application.status)}
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                          <MapPin className="w-4 h-4" />
                          {application.propertyAddress}
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            Applied:{" "}
                            {new Date(
                              application.submittedDate
                            ).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            Last Activity:{" "}
                            {new Date(
                              application.lastActivity
                            ).toLocaleDateString()}
                          </div>
                        </div>

                        {/* Tenant Info */}
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <User className="w-5 h-5 text-gray-600" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {application.tenantName}
                              </p>
                              <div className="flex items-center gap-3 text-xs text-gray-600">
                                <span className="flex items-center gap-1">
                                  <Mail className="w-3 h-3" />
                                  {application.tenantEmail}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Phone className="w-3 h-3" />
                                  {application.tenantPhone}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium text-gray-900">
                                {application.tenantRating}
                              </span>
                            </div>
                            <button
                              onClick={() =>
                                setTenantInfoModal({
                                  open: true,
                                  applicationId: application.id,
                                })
                              }
                              className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors"
                            >
                              View Details
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Offer Details & Actions */}
                  <div className="space-y-4">
                    {/* Price Information */}
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">
                            Listed Price:
                          </span>
                          <span className="text-sm font-medium text-gray-900">
                            ${application.originalPrice.toLocaleString()}/month
                          </span>
                        </div>

                        {application.tenantLastOffer && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-blue-600">
                              Tenant's Offer:
                            </span>
                            <span className="text-sm font-bold text-blue-600">
                              ${application.tenantLastOffer.toLocaleString()}
                              /month
                            </span>
                          </div>
                        )}

                        {application.myLastOffer && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-green-600">
                              My Counter Offer:
                            </span>
                            <span className="text-sm font-bold text-green-600">
                              ${application.myLastOffer.toLocaleString()}/month
                            </span>
                          </div>
                        )}

                        <div className="pt-2 border-t border-blue-200">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-900">
                              Current Offer:
                            </span>
                            <span className="text-lg font-bold text-blue-600">
                              ${application.currentOffer.toLocaleString()}/month
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                      {application.status === "pending" ||
                      application.status === "under_negotiation" ? (
                        <>
                          <button
                            onClick={() =>
                              handleAcceptApplication(application.id)
                            }
                            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors myButton"
                          >
                            <CheckCircle className="w-4 h-4" />
                            Accept ${application.currentOffer.toLocaleString()}
                            /month
                          </button>

                          <button
                            onClick={() => handleNegotiate(application.id)}
                            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors myButton"
                          >
                            <ArrowUpDown className="w-4 h-4" />
                            {application.status === "pending"
                              ? "Counter Offer"
                              : "New Counter Offer"}
                          </button>

                          <button
                            onClick={() =>
                              handleDeclineApplication(application.id)
                            }
                            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-lg hover:bg-red-100 transition-colors myButton"
                          >
                            <XCircle className="w-4 h-4" />
                            Decline Application
                          </button>
                        </>
                      ) : application.status === "agreed" &&
                        application.canMessage ? (
                        <button
                          onClick={() =>
                            setMessageModal({
                              open: true,
                              applicationId: application.id,
                            })
                          }
                          className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors myButton"
                        >
                          <MessageSquare className="w-4 h-4" />
                          Message Tenant
                        </button>
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-sm text-gray-500">
                            {application.status === "accepted" &&
                              "Application accepted"}
                            {application.status === "declined" &&
                              "Application declined"}
                            {application.status === "agreed" &&
                              !application.canMessage &&
                              "Terms agreed - messaging will be available soon"}
                          </p>
                        </div>
                      )}

                      {/* View History Button */}
                      <button
                        onClick={() =>
                          setHistoryModal({
                            open: true,
                            applicationId: application.id,
                          })
                        }
                        className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors myButton"
                      >
                        <History className="w-4 h-4" />
                        View Negotiation History
                      </button>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Modals */}
        <ApplicationModals
          negotiateModal={negotiateModal}
          setNegotiateModal={setNegotiateModal}
          historyModal={historyModal}
          setHistoryModal={setHistoryModal}
          messageModal={messageModal}
          setMessageModal={setMessageModal}
          confirmModal={confirmModal}
          setConfirmModal={setConfirmModal}
          tenantInfoModal={tenantInfoModal}
          setTenantInfoModal={setTenantInfoModal}
          negotiateAmount={negotiateAmount}
          setNegotiateAmount={setNegotiateAmount}
          negotiateMessage={negotiateMessage}
          setNegotiateMessage={setNegotiateMessage}
          chatMessage={chatMessage}
          setChatMessage={setChatMessage}
          isLoading={isLoading}
          applications={applications}
          submitNegotiation={submitNegotiation}
          confirmAction={confirmAction}
        />
      </div>
    </div>
  );
};

export default ApplicationsManagement;
