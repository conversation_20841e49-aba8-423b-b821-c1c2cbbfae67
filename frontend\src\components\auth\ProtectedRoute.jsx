import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../ui/LoadingSpinner';
import AccessDenied from './AccessDenied';

const ProtectedRoute = ({ 
  children, 
  requiredRole = null, 
  requiredRoles = [], 
  requiredPermission = null,
  requiredPermissions = [],
  fallbackPath = '/login',
  showAccessDenied = true 
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check role requirements
  const hasRequiredRole = () => {
    // If specific role is required
    if (requiredRole) {
      return user.role === requiredRole;
    }

    // If any of multiple roles is required
    if (requiredRoles.length > 0) {
      return requiredRoles.includes(user.role);
    }

    // No role requirement
    return true;
  };

  // Check permission requirements
  const hasRequiredPermissions = () => {
    // If specific permission is required
    if (requiredPermission) {
      return user.permissions?.includes(requiredPermission);
    }

    // If multiple permissions are required (all must be present)
    if (requiredPermissions.length > 0) {
      return requiredPermissions.every(permission => 
        user.permissions?.includes(permission)
      );
    }

    // No permission requirement
    return true;
  };

  // Check if user meets all requirements
  const hasAccess = hasRequiredRole() && hasRequiredPermissions();

  if (!hasAccess) {
    if (showAccessDenied) {
      return (
        <AccessDenied 
          requiredRole={requiredRole}
          requiredRoles={requiredRoles}
          requiredPermission={requiredPermission}
          requiredPermissions={requiredPermissions}
          userRole={user.role}
          userPermissions={user.permissions}
        />
      );
    } else {
      // Redirect to appropriate dashboard based on user role
      const redirectPath = getUserDashboard(user.role);
      return <Navigate to={redirectPath} replace />;
    }
  }

  // User has access, render the protected component
  return children;
};

// Helper function to get user's default dashboard
const getUserDashboard = (role) => {
  switch (role) {
    case 'landlord':
      return '/landlord/dashboard';
    case 'tenant':
      return '/tenant/dashboard';
    case 'property_manager':
      return '/property-manager/dashboard';
    default:
      return '/';
  }
};

// Role-specific protected route components for convenience
export const LandlordRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRole="landlord" {...props}>
    {children}
  </ProtectedRoute>
);

export const TenantRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRole="tenant" {...props}>
    {children}
  </ProtectedRoute>
);

export const PropertyManagerRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRole="property_manager" {...props}>
    {children}
  </ProtectedRoute>
);

// Multi-role protected routes
export const LandlordOrManagerRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRoles={['landlord', 'property_manager']} {...props}>
    {children}
  </ProtectedRoute>
);

export const AuthenticatedRoute = ({ children, ...props }) => (
  <ProtectedRoute {...props}>
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;
