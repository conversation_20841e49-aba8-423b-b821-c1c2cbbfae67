.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #2d5596 #1d3658;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1d3658;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #2d5596;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #3a6bc0;
}

.myButton {
  background-color: #155dfc;
  color: #fff;
  border: none;
  padding: 8px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(45, 85, 150, 0.12);
  transition: 0.3s ease-in-out;
}
.myButton:hover {
  background: #1447e6;
  box-shadow: 0 4px 16px rgba(58, 107, 192, 0.18);
  transform: scale(1);
}


body {
  --sb-track-color: #dcdcdc;
  --sb-thumb-color: #2b7fff;
  --sb-size: 7px;
}

body::-webkit-scrollbar {
  width: var(--sb-size)
}

body::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 12px;
}

body::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 12px;
  
}

@supports not selector(::-webkit-scrollbar) {
  body {
    scrollbar-color: var(--sb-thumb-color)
                     var(--sb-track-color);
  }
}