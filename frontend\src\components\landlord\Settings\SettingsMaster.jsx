import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  Settings,
  User,
  Building,
  Bell,
  Shield,
  CreditCard,
  FileText,
  MessageSquare,
  Palette,
  Save,
  X,
  Edit,
  Camera,
  Phone,
  Mail,
  MapPin,
  Key,
  Smartphone,
  History,
  DollarSign,
  Clock,
  Globe,
  Moon,
  Sun,
  Monitor,
  Check,
  AlertCircle,
  Eye,
  EyeOff,
  Upload,
  Download,
  Trash2,
  Plus,
  Home,
  Lock,
  Database,
} from "lucide-react";

const SettingsMaster = () => {
  // Active section state
  const [activeSection, setActiveSection] = useState("profile");

  // Modal states
  const [profileEditModal, setProfileEditModal] = useState(false);
  const [passwordChangeModal, setPasswordChangeModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState({
    open: false,
    title: "",
    message: "",
    action: null,
  });

  // Form states
  const [profileData, setProfileData] = useState({
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Main Street, City, State 12345",
    company: "Smith Properties LLC",
    profilePicture: null,
  });

  const [propertySettings, setPropertySettings] = useState({
    defaultLeaseTerm: "12",
    rentDueDate: "1",
    lateFeeAmount: "50",
    lateFeeGracePeriod: "5",
    securityDepositMultiplier: "1",
    maintenanceResponseTime: "24",
    allowPetsByDefault: false,
    requireRentersInsurance: true,
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: {
      rentPayments: true,
      maintenanceRequests: true,
      tenantMessages: true,
      leaseExpirations: true,
      systemUpdates: false,
    },
    smsNotifications: {
      urgentMaintenance: true,
      overduePayments: true,
      emergencyContacts: true,
    },
    pushNotifications: {
      realTimeAlerts: true,
      dailySummary: true,
    },
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    loginNotifications: true,
    sessionTimeout: "30",
    passwordLastChanged: "2024-05-15",
  });

  const [paymentSettings, setPaymentSettings] = useState({
    bankAccount: {
      accountName: "Smith Properties LLC",
      routingNumber: "****5678",
      accountNumber: "****1234",
    },
    processingFees: {
      creditCard: "2.9",
      bankTransfer: "0.5",
      paypal: "3.2",
    },
    autoDeposit: true,
    paymentReminders: true,
  });

  const [documentSettings, setDocumentSettings] = useState({
    defaultTemplates: {
      leaseAgreement: "Standard Residential Lease",
      moveInChecklist: "Property Inspection Form",
      moveOutChecklist: "Move-Out Inspection",
    },
    retentionPeriod: "7",
    autoBackup: true,
    cloudStorage: "enabled",
  });

  const [communicationSettings, setCommunicationSettings] = useState({
    autoResponses: {
      maintenanceRequests:
        "Thank you for your maintenance request. We will respond within 24 hours.",
      generalInquiries:
        "Thank you for contacting us. We will get back to you soon.",
    },
    tenantPortalEnabled: true,
    allowTenantMessaging: true,
    businessHours: {
      start: "09:00",
      end: "17:00",
      timezone: "EST",
    },
  });

  const [systemSettings, setSystemSettings] = useState({
    theme: "light",
    language: "en",
    timezone: "America/New_York",
    dashboardLayout: "grid",
    compactMode: false,
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Loading and notification states
  const [isLoading, setIsLoading] = useState(false);

  // Settings sections configuration
  const settingsSections = [
    {
      id: "profile",
      name: "Profile Settings",
      icon: User,
      description: "Personal information and contact details",
    },
    {
      id: "property",
      name: "Property Management",
      icon: Building,
      description: "Default lease terms and property settings",
    },
    {
      id: "notifications",
      name: "Notifications",
      icon: Bell,
      description: "Email, SMS, and push notification preferences",
    },
    {
      id: "security",
      name: "Security",
      icon: Shield,
      description: "Password, 2FA, and security settings",
    },
    {
      id: "payment",
      name: "Payment & Billing",
      icon: CreditCard,
      description: "Bank details and payment processing",
    },
    {
      id: "documents",
      name: "Document Management",
      icon: FileText,
      description: "Templates, storage, and retention policies",
    },
    {
      id: "communication",
      name: "Tenant Communication",
      icon: MessageSquare,
      description: "Auto-responses and communication settings",
    },
    {
      id: "system",
      name: "System Preferences",
      icon: Palette,
      description: "Theme, language, and dashboard customization",
    },
  ];

  // Handler functions
  const handleSaveSettings = (section) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);

      // Show success notification
      const notification = document.createElement("div");
      notification.className =
        "fixed z-50 px-6 py-3 text-white bg-green-500 rounded-lg shadow-lg top-4 right-4";
      notification.textContent = `${
        settingsSections.find((s) => s.id === section)?.name
      } updated successfully!`;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 4000);
    }, 1500);
  };

  const handlePasswordChange = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert("New passwords do not match");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      alert("Password must be at least 8 characters long");
      return;
    }

    setConfirmationModal({
      open: true,
      title: "Change Password",
      message:
        "Are you sure you want to change your password? You will need to log in again.",
      action: () => {
        setIsLoading(true);
        setTimeout(() => {
          setIsLoading(false);
          setPasswordChangeModal(false);
          setPasswordData({
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
          });
          setConfirmationModal({
            open: false,
            title: "",
            message: "",
            action: null,
          });

          const notification = document.createElement("div");
          notification.className =
            "fixed z-50 px-6 py-3 text-white bg-green-500 rounded-lg shadow-lg top-4 right-4";
          notification.textContent = "Password changed successfully!";
          document.body.appendChild(notification);
          setTimeout(() => notification.remove(), 4000);
        }, 2000);
      },
    });
  };

  const validatePassword = (password) => {
    const hasLength = password.length >= 8;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      isValid: hasLength && hasUpper && hasLower && hasNumber && hasSpecial,
      checks: { hasLength, hasUpper, hasLower, hasNumber, hasSpecial },
    };
  };

  const passwordValidation = validatePassword(passwordData.newPassword);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="p-6 mx-auto space-y-6 max-w-7xl">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Settings className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
              <p className="text-gray-600">Manage your account and system preferences</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => handleSaveSettings(activeSection)}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white transition-colors bg-blue-600 rounded-lg myButton hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white rounded-full border-t-transparent animate-spin"></div>
              ) : (
                <Save size={16} />
              )}
              Save Changes
            </button>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Profile</p>
                <p className="text-2xl font-bold text-gray-900">Complete</p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Security</p>
                <p className="text-2xl font-bold text-gray-900">{securitySettings.twoFactorEnabled ? "Enhanced" : "Basic"}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-100 rounded-lg">
                <Bell className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Notifications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.values(notificationSettings.emailNotifications).filter(Boolean).length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6 border-0 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Palette className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Theme</p>
                <p className="text-2xl font-bold text-gray-900 capitalize">{systemSettings.theme}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <Card className="p-4 border-0 shadow-sm">
              <nav className="space-y-2">
                {settingsSections.map((section) => {
                  const IconComponent = section.icon;
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center gap-3 px-3 py-3 text-left rounded-lg transition-colors ${
                        activeSection === section.id
                          ? "bg-blue-500 text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      <IconComponent size={18} />
                      <div className="flex-1">
                        <span className="block text-sm font-medium">{section.name}</span>
                        <span className="block text-xs opacity-75">{section.description}</span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </Card>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            {activeSection === "profile" && (
              <Card className="p-6 border-0 shadow-sm">
                <h3 className="mb-6 text-lg font-semibold text-gray-900">Profile Settings</h3>

                <div className="space-y-6">
                  {/* Profile Picture */}
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <div className="flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full">
                        {profileData.profilePicture ? (
                          <img
                            src={profileData.profilePicture}
                            alt="Profile"
                            className="object-cover w-20 h-20 rounded-full"
                          />
                        ) : (
                          <User className="w-10 h-10 text-blue-600" />
                        )}
                      </div>
                      <button className="absolute p-1 text-white transition-colors bg-blue-600 rounded-full -bottom-1 -right-1 hover:bg-blue-700">
                        <Camera size={14} />
                      </button>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Profile Picture</h4>
                      <p className="text-sm text-gray-600">Upload a professional photo</p>
                      <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                        Change Photo
                      </button>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        First Name
                      </label>
                      <input
                        type="text"
                        value={profileData.firstName}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            firstName: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        Last Name
                      </label>
                      <input
                        type="text"
                        value={profileData.lastName}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            lastName: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={profileData.email}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            email: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            phone: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        Address
                      </label>
                      <input
                        type="text"
                        value={profileData.address}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            address: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block mb-2 text-sm font-medium text-gray-700">
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={profileData.company}
                        onChange={(e) =>
                          setProfileData((prev) => ({
                            ...prev,
                            company: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end pt-4">
                    <button
                      onClick={() => setPasswordChangeModal(true)}
                      className="flex items-center gap-2 px-4 py-2 mr-3 text-sm font-medium text-blue-600 transition-colors rounded-lg bg-blue-50 hover:bg-blue-100"
                    >
                      <Key size={16} />
                      Change Password
                    </button>
                  </div>
                </div>
              </Card>
            )}

            {/* Placeholder for other sections */}
            {activeSection !== "profile" && (
              <Card className="p-12 text-center border-0 shadow-sm">
                <div className="space-y-4">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto bg-gray-100 rounded-full">
                    {settingsSections.find(s => s.id === activeSection)?.icon && (
                      React.createElement(settingsSections.find(s => s.id === activeSection).icon, {
                        className: "w-8 h-8 text-gray-400"
                      })
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {settingsSections.find(s => s.id === activeSection)?.name}
                  </h3>
                  <p className="text-gray-600">
                    {settingsSections.find(s => s.id === activeSection)?.description}
                  </p>
                  <p className="text-sm text-gray-500">
                    This section is coming soon. Stay tuned for updates!
                  </p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Current Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={accountSettings.currentPassword}
                onChange={(e) =>
                  setAccountSettings((prev) => ({
                    ...prev,
                    currentPassword: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3"
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              New Password
            </label>
            <div className="relative">
              <input
                type={showNewPassword ? "text" : "password"}
                value={accountSettings.newPassword}
                onChange={(e) =>
                  setAccountSettings((prev) => ({
                    ...prev,
                    newPassword: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3"
              >
                {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <input
              type="password"
              value={accountSettings.confirmPassword}
              onChange={(e) =>
                setAccountSettings((prev) => ({
                  ...prev,
                  confirmPassword: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </Card>

      <Card className="p-6 border-0">
        <h3 className="flex items-center gap-2 mb-4 text-lg font-semibold text-gray-900">
          <Bell className="w-5 h-5 text-blue-600" />
          Notification Preferences
        </h3>

        <div className="space-y-4">
          {Object.entries(accountSettings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900 capitalize">
                  {key.replace(/([A-Z])/g, " $1").trim()} Notifications
                </label>
                <p className="text-xs text-gray-500">
                  Receive notifications via{" "}
                  {key === "push" ? "browser push" : key}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) =>
                    setAccountSettings((prev) => ({
                      ...prev,
                      notifications: {
                        ...prev.notifications,
                        [key]: e.target.checked,
                      },
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </Card>

      <div className="flex justify-end">
        <button
          onClick={() => handleSave("Account")}
          className="flex items-center gap-2 px-6 py-2 myButton"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white rounded-full border-t-transparent animate-spin"></div>
          ) : (
            <Save size={16} />
          )}
          Save Account Settings
        </button>
      </div>
    </div>
  );

  const renderPropertySettings = () => (
    <div className="space-y-6">
      <Card className="p-6 border-0">
        <h3 className="flex items-center gap-2 mb-4 text-lg font-semibold text-gray-900">
          <DollarSign className="w-5 h-5 text-blue-600" />
          Default Payment Settings
        </h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Default Rent Amount
            </label>
            <input
              type="number"
              value={propertySettings.defaultRentAmount}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  defaultRentAmount: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Currency
            </label>
            <select
              value={propertySettings.currency}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  currency: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="AED">AED - UAE Dirham</option>
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
            </select>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Payment Due Day
            </label>
            <select
              value={propertySettings.paymentDueDay}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  paymentDueDay: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {Array.from({ length: 28 }, (_, i) => i + 1).map((day) => (
                <option key={day} value={day.toString()}>
                  {day}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      <Card className="p-6 border-0">
        <h3 className="flex items-center gap-2 mb-4 text-lg font-semibold text-gray-900">
          <Clock className="w-5 h-5 text-blue-600" />
          Late Fee Configuration
        </h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Late Fee Percentage (%)
            </label>
            <input
              type="number"
              step="0.1"
              value={propertySettings.lateFeePercentage}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  lateFeePercentage: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Grace Period (Days)
            </label>
            <input
              type="number"
              value={propertySettings.lateFeeGracePeriod}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  lateFeeGracePeriod: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Security Deposit Multiplier
            </label>
            <select
              value={propertySettings.securityDepositMultiplier}
              onChange={(e) =>
                setPropertySettings((prev) => ({
                  ...prev,
                  securityDepositMultiplier: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="0.5">0.5x Monthly Rent</option>
              <option value="1">1x Monthly Rent</option>
              <option value="1.5">1.5x Monthly Rent</option>
              <option value="2">2x Monthly Rent</option>
            </select>
          </div>
        </div>
      </Card>

      <div className="flex justify-end">
        <button
          onClick={() => handleSave("Property")}
          className="flex items-center gap-2 px-6 py-2 myButton"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white rounded-full border-t-transparent animate-spin"></div>
          ) : (
            <Save size={16} />
          )}
          Save Property Settings
        </button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "account":
        return renderAccountSettings();
      case "property":
        return renderPropertySettings();
      case "email":
        return (
          <div className="p-8 text-center text-gray-500">
            Email Settings - Coming Soon
          </div>
        );
      case "payment":
        return (
          <div className="p-8 text-center text-gray-500">
            Payment Settings - Coming Soon
          </div>
        );
      case "system":
        return (
          <div className="p-8 text-center text-gray-500">
            System Settings - Coming Soon
          </div>
        );
      case "security":
        return (
          <div className="p-8 text-center text-gray-500">
            Security Settings - Coming Soon
          </div>
        );
      default:
        return renderAccountSettings();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="p-6">
        {/* Header */}
        <Card className="p-6 mb-6 border-0 bg-gradient-to-br from-blue-50 via-white to-blue-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
              <p className="text-sm text-gray-600">
                Manage your Property Harmony preferences
              </p>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="p-4 border-0">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? "bg-blue-500 text-white"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <tab.icon size={18} />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                ))}
              </nav>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">{renderContent()}</div>
        </div>
      </div>
    </div>
  );
};

export default SettingsMaster;
