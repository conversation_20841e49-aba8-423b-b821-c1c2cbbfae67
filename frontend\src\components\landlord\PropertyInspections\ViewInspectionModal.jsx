import React from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  X,
  Building,
  User,
  Calendar,
  ClipboardCheck,
  AlertTriangle,
  CheckCircle,
  FileText,
} from "lucide-react";
import {
  inspectionStatuses,
  priorityLevels,
  conditionLevels,
} from "@/data/landlord/propertyInspection/data";

const ViewInspectionModal = ({ open, onClose, inspection }) => {
  if (!inspection) return null;

  const getStatusStyle = (status) => {
    const statusObj = inspectionStatuses.find((s) => s.value === status);
    return statusObj
      ? `${statusObj.bgColor} ${statusObj.textColor}`
      : "bg-gray-100 text-gray-700";
  };

  const getPriorityStyle = (priority) => {
    const priorityObj = priorityLevels.find((p) => p.value === priority);
    return priorityObj
      ? `${priorityObj.bgColor} ${priorityObj.textColor}`
      : "bg-gray-100 text-gray-700";
  };

  const getConditionStyle = (condition) => {
    const conditionObj = conditionLevels.find((c) => c.value === condition);
    return conditionObj
      ? `${conditionObj.bgColor} ${conditionObj.textColor}`
      : "bg-gray-100 text-gray-700";
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-3xl md:max-w-2xl max-h-[80vh] bg-white border-0 rounded-lg shadow-xl overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-800">
              {inspection.status === "Completed"
                ? "Inspection Report"
                : "Inspection Details"}
            </h2>
          </div>

          {(inspection.status === "Completed" ||
            inspection.status === "Report Generated") &&
          inspection.inspectionReport ? (
            // Completed Inspection Report View
            <div className="space-y-6">
              {/* Report Summary */}
              <div className="p-4 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100">
                <h3 className="mb-4 text-lg font-medium text-blue-900">
                  Inspection Report Summary
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Property:
                    </span>
                    <p className="text-blue-900">{inspection.propertyName}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Tenant:
                    </span>
                    <p className="text-blue-900">{inspection.tenantName}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Inspector:
                    </span>
                    <p className="text-blue-900">{inspection.inspectorName}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Report Date:
                    </span>
                    <p className="text-blue-900">
                      {inspection.inspectionReport.generatedDate}
                    </p>
                  </div>
                </div>
                <div className="mt-4 flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Overall Score:
                    </span>
                    <span className="ml-2 text-2xl font-bold text-blue-600">
                      {inspection.inspectionReport.overallScore}/100
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-blue-700">
                      Generated By:
                    </span>
                    <span className="ml-2 text-blue-900">
                      {inspection.inspectionReport.generatedBy}
                    </span>
                  </div>
                </div>
              </div>

              {/* Room Assessments */}
              {inspection.inspectionReport.roomAssessments &&
                inspection.inspectionReport.roomAssessments.length > 0 && (
                  <div>
                    <h3 className="mb-4 text-lg font-medium text-gray-800">
                      Room-by-Room Assessment
                    </h3>
                    <div className="space-y-3">
                      {inspection.inspectionReport.roomAssessments.map(
                        (room, index) => (
                          <div
                            key={index}
                            className="p-4 border border-gray-200 rounded-lg"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-gray-800">
                                {room.room}
                              </h4>
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-gray-600">
                                  Score: {room.score}/100
                                </span>
                                <span
                                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConditionStyle(
                                    room.condition
                                  )}`}
                                >
                                  {room.condition}
                                </span>
                              </div>
                            </div>
                            <p className="mb-2 text-sm text-gray-600">
                              {room.notes}
                            </p>

                            {/* Room Photos */}
                            {room.photos && room.photos.length > 0 && (
                              <div className="mb-4">
                                <h5 className="mb-2 text-sm font-medium text-gray-700">
                                  Photos ({room.photos.length})
                                </h5>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                  {room.photos.map((photo, photoIndex) => (
                                    <div
                                      key={photoIndex}
                                      className="relative group"
                                    >
                                      <div className="relative overflow-hidden bg-gray-100 rounded-lg aspect-square">
                                        <img
                                          src={photo.url || photo}
                                          alt={
                                            photo.name ||
                                            `Room photo ${photoIndex + 1}`
                                          }
                                          className="object-cover w-full h-full cursor-pointer hover:scale-105 transition-transform"
                                          onClick={() => {
                                            // Optional: Add lightbox functionality here
                                            window.open(
                                              photo.url || photo,
                                              "_blank"
                                            );
                                          }}
                                        />
                                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity flex items-center justify-center">
                                          <div className="opacity-0 group-hover:opacity-100 p-2 bg-white bg-opacity-90 rounded-full transition-opacity">
                                            <svg
                                              className="w-4 h-4 text-gray-700"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                      </div>
                                      {photo.name && (
                                        <p className="mt-1 text-xs text-gray-500 truncate">
                                          {photo.name}
                                        </p>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {room.issues && room.issues.length > 0 && (
                              <div>
                                <span className="flex items-center gap-1 text-sm font-medium text-red-600">
                                  <AlertTriangle size={14} />
                                  Issues Found:
                                </span>
                                <div className="mt-2 space-y-1">
                                  {room.issues.map((issue, issueIndex) => (
                                    <div
                                      key={issueIndex}
                                      className="flex items-center justify-between p-2 bg-red-50 rounded"
                                    >
                                      <div className="flex items-center gap-2">
                                        <span
                                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                                            issue.severity === "Critical"
                                              ? "bg-red-100 text-red-700"
                                              : issue.severity === "Major"
                                              ? "bg-orange-100 text-orange-700"
                                              : "bg-yellow-100 text-yellow-700"
                                          }`}
                                        >
                                          {issue.severity}
                                        </span>
                                        <span className="text-sm text-gray-900">
                                          {issue.description}
                                        </span>
                                      </div>
                                      <span className="text-sm text-gray-600">
                                        AED {issue.cost}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}

              {/* Landlord Observations and Maintenance Recommendations */}
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <h3 className="mb-4 text-lg font-medium text-gray-800">
                    Landlord Observations
                  </h3>
                  <div className="p-4 rounded-lg bg-blue-50">
                    <p className="text-sm text-gray-700">
                      {inspection.inspectionReport.landlordObservations ||
                        "Property is in good condition overall. Minor maintenance needed."}
                    </p>
                  </div>
                </div>
                <div>
                  <h3 className="mb-4 text-lg font-medium text-gray-800">
                    Maintenance Recommendations
                  </h3>
                  <div className="p-4 rounded-lg bg-green-50">
                    {inspection.inspectionReport.maintenanceRecommendations &&
                    inspection.inspectionReport.maintenanceRecommendations
                      .length > 0 ? (
                      <div className="space-y-2">
                        {inspection.inspectionReport.maintenanceRecommendations.map(
                          (rec, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-white rounded"
                            >
                              <div className="flex items-center gap-2">
                                <span
                                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                                    rec.priority === "High"
                                      ? "bg-red-100 text-red-700"
                                      : rec.priority === "Medium"
                                      ? "bg-yellow-100 text-yellow-700"
                                      : "bg-green-100 text-green-700"
                                  }`}
                                >
                                  {rec.priority}
                                </span>
                                <span className="text-sm text-gray-900">
                                  {rec.description}
                                </span>
                              </div>
                              <span className="text-sm text-gray-600">
                                AED {rec.estimatedCost}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-700">
                        No specific recommendations at this time.
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Next Inspection */}
              {inspection.inspectionReport.nextInspectionRecommended && (
                <div>
                  <h3 className="mb-4 text-lg font-medium text-gray-800">
                    Next Inspection Recommended
                  </h3>
                  <div className="flex items-center gap-2 p-4 rounded-lg bg-yellow-50">
                    <Calendar className="w-5 h-5 text-yellow-600" />
                    <span className="text-sm font-medium text-gray-700">
                      Recommended for{" "}
                      {inspection.inspectionReport.nextInspectionRecommended}
                    </span>
                  </div>
                </div>
              )}

              {/* Tenant Comments */}
              {inspection.tenantComments &&
                inspection.tenantComments.length > 0 && (
                  <div>
                    <h3 className="mb-4 text-lg font-medium text-gray-800">
                      Tenant Comments
                    </h3>
                    <div className="space-y-2">
                      {inspection.tenantComments.map((comment, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium text-gray-900">
                              Tenant Response
                            </span>
                            <span className="text-xs text-gray-500">
                              {comment.date}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700">
                            {comment.comment}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          ) : (
            // Regular Inspection Details View
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Property
                    </label>
                    <div className="flex items-center gap-2">
                      <Building className="w-4 h-4 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-800">
                          {inspection.propertyName}
                        </p>
                        <p className="text-sm text-gray-600">
                          {inspection.propertyAddress}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Tenant
                    </label>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <div>
                        <p className="text-gray-800">{inspection.tenantName}</p>
                        <p className="text-sm text-gray-600">
                          {inspection.tenantEmail}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Inspector
                    </label>
                    <div className="flex items-center gap-2">
                      <ClipboardCheck className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-800">
                        {inspection.inspectorName}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Inspection Type
                    </label>
                    <p className="text-gray-800">{inspection.inspectionType}</p>
                  </div>

                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Priority
                    </label>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityStyle(
                        inspection.priority
                      )}`}
                    >
                      {inspection.priority}
                    </span>
                  </div>

                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Status
                    </label>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusStyle(
                        inspection.status
                      )}`}
                    >
                      {inspection.status}
                    </span>
                  </div>

                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-600">
                      Requested Date
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-800">
                        {inspection.requestedDate}
                      </p>
                    </div>
                  </div>

                  {inspection.scheduledDate && (
                    <div>
                      <label className="block mb-1 text-sm font-medium text-gray-600">
                        Scheduled Date
                      </label>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <p className="text-gray-800">
                          {inspection.scheduledDate}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {(inspection.notes || inspection.landlordNotes) && (
                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-600">
                    Inspection Purpose & Notes
                  </label>
                  <div className="p-4 rounded-lg bg-gray-50">
                    <p className="text-gray-800">
                      {inspection.landlordNotes || inspection.notes}
                    </p>
                  </div>
                </div>
              )}

              {/* Tenant Response Information */}
              {inspection.tenantResponse && (
                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-600">
                    Tenant Response
                  </label>
                  <div className="p-4 rounded-lg bg-blue-50">
                    <div className="flex items-center justify-between mb-2">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          inspection.tenantResponse === "accepted"
                            ? "bg-green-100 text-green-700"
                            : inspection.tenantResponse === "declined"
                            ? "bg-red-100 text-red-700"
                            : "bg-orange-100 text-orange-700"
                        }`}
                      >
                        {inspection.tenantResponse === "accepted"
                          ? "Accepted"
                          : inspection.tenantResponse === "declined"
                          ? "Declined"
                          : "Reschedule Requested"}
                      </span>
                      <span className="text-xs text-gray-500">
                        {inspection.tenantResponseDate}
                      </span>
                    </div>

                    {inspection.tenantDeclineReason && (
                      <div className="mt-2">
                        <span className="text-sm font-medium text-gray-700">
                          Reason:
                        </span>
                        <p className="text-sm text-gray-600 mt-1">
                          {inspection.tenantDeclineReason}
                        </p>
                      </div>
                    )}

                    {inspection.tenantSuggestedTimes &&
                      inspection.tenantSuggestedTimes.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm font-medium text-gray-700">
                            Suggested Times:
                          </span>
                          <div className="mt-1 space-y-1">
                            {inspection.tenantSuggestedTimes.map(
                              (time, index) => (
                                <div
                                  key={index}
                                  className="text-sm text-gray-600"
                                >
                                  {time.date} at {time.time}
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end pt-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-gray-100 rounded-md cursor-pointer hover:bg-gray-200"
            >
              Close
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewInspectionModal;
